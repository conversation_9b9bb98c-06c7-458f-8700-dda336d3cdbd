package main

import (
	"crypto/sha256"
	"crypto/subtle"
	"log"

	"github.com/applegold/articles-backend/database"
	"github.com/applegold/articles-backend/routes"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/keyauth"
)

const (
	apiKey = "CZaeR90VPPHtvyOVTVQA1QtYK50PLca3SslcKPdse3k5F0gZ9HGdhctkbpF5yn3G"
)

func setUpRoutes(app *fiber.App) {

	authMiddleware := keyauth.New(keyauth.Config{
		Validator: func(c *fiber.Ctx, key string) (bool, error) {
			hashedAPIKey := sha256.Sum256([]byte(apiKey))
			hashedKey := sha256.Sum256([]byte(key))

			if subtle.ConstantTimeCompare(hashedAPIKey[:], hashedKey[:]) == 1 {
				return true, nil
			}
			return false, keyauth.ErrMissingOrMalformedAPIKey
		},
	})

	app.Get("/", routes.Hello)

	app.Get("/allowed", authMiddleware, routes.Allowed)

	app.Get("/articles", routes.GetArticles)

	app.Get("/articles-single/:id", routes.GetArticleSingle)

	app.Delete("/articles-delete/:id", authMiddleware, routes.DeleteArticle)

	app.Get("/contacts-get", routes.GetContacts)

	app.Post("/articles-approve/:id", routes.ArticleApprove)

}

func main() {
	log.Println("Starting Articles Backend Server...")

	err := database.Connect()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	app := fiber.New()

	setUpRoutes(app)

	log.Println("Server starting on port 6022...")
	log.Fatal(app.Listen("0.0.0.0:6022"))
}
