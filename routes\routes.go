package routes

import (
	"database/sql"
	"log"
	"time"

	"github.com/applegold/articles-backend/database"
	"github.com/gofiber/fiber/v2"
)

// Article represents the article table structure
type Article struct {
	ID           int       `json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	AuthorName   *string   `json:"author_name"`
	EmailAddress *string   `json:"email_address"`
	CategoryID   *int      `json:"category_id"`
	ArticleTitle *string   `json:"article_title"`
	ArticleBody  *string   `json:"article_body"`
	Message      *string   `json:"message"`
	AcceptTC     *bool     `json:"accept_tc"`
	Paid         *bool     `json:"paid"`
	Approved     *bool     `json:"approved"`
	URL          *string   `json:"url"`
	Keyword      *string   `json:"keyword"`
}

// Contact represents the contact table structure
type Contact struct {
	ID        int       `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	FullName  *string   `json:"full_name"`
	Company   *string   `json:"company"`
	Subject   *string   `json:"subject"`
	Message   *string   `json:"message"`
	Email     *string   `json:"email"`
}

// Category represents the categories table structure
type Category struct {
	ID           int    `json:"id"`
	CategoryName string `json:"category_name"`
	Link         string `json:"link"`
}

func Hello(c *fiber.Ctx) error {

	database.DBConn.Query("SELECT * FROM users")

	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func GetArticles(c *fiber.Ctx) error {
	log.Printf("GetArticles endpoint called")

	// Check if database connection is available
	if database.DBConn == nil {
		log.Printf("Database connection is nil")
		return c.Status(500).JSON(fiber.Map{
			"error": "Database connection not available",
		})
	}

	// Query to get all articles from the database
	query := `SELECT id, created_at, author_name, email_address, category_id,
			  article_title, article_body, message, accept_tc, paid, approved, url, keyword
			  FROM article ORDER BY created_at DESC`

	log.Printf("Executing query: %s", query)
	rows, err := database.DBConn.Query(query)
	if err != nil {
		log.Printf("Error querying articles: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch articles",
		})
	}
	defer rows.Close()

	var articles []Article

	for rows.Next() {
		var article Article
		var authorName, emailAddress, articleTitle, articleBody, message, url, keyword sql.NullString
		var categoryID sql.NullInt64
		var acceptTC, paid, approved sql.NullBool

		err := rows.Scan(
			&article.ID,
			&article.CreatedAt,
			&authorName,
			&emailAddress,
			&categoryID,
			&articleTitle,
			&articleBody,
			&message,
			&acceptTC,
			&paid,
			&approved,
			&url,
			&keyword,
		)

		if err != nil {
			log.Printf("Error scanning article row: %v", err)
			continue
		}

		// Handle nullable fields
		if authorName.Valid {
			article.AuthorName = &authorName.String
		}
		if emailAddress.Valid {
			article.EmailAddress = &emailAddress.String
		}
		if categoryID.Valid {
			categoryIDInt := int(categoryID.Int64)
			article.CategoryID = &categoryIDInt
		}
		if articleTitle.Valid {
			article.ArticleTitle = &articleTitle.String
		}
		if articleBody.Valid {
			article.ArticleBody = &articleBody.String
		}
		if message.Valid {
			article.Message = &message.String
		}
		if acceptTC.Valid {
			article.AcceptTC = &acceptTC.Bool
		}
		if paid.Valid {
			article.Paid = &paid.Bool
		}
		if approved.Valid {
			article.Approved = &approved.Bool
		}
		if url.Valid {
			article.URL = &url.String
		}
		if keyword.Valid {
			article.Keyword = &keyword.String
		}

		articles = append(articles, article)
	}

	if err = rows.Err(); err != nil {
		log.Printf("Error iterating over article rows: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to process articles",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    articles,
		"count":   len(articles),
	})
}

func GetArticleSingle(c *fiber.Ctx) error {
	// Get article ID from URL parameter
	articleID := c.Params("id")
	if articleID == "" {
		return c.Status(400).JSON(fiber.Map{
			"error": "Article ID is required",
		})
	}

	log.Printf("Getting single article with ID: %s", articleID)

	// Check if database connection is available
	if database.DBConn == nil {
		log.Printf("Database connection is nil")
		return c.Status(500).JSON(fiber.Map{
			"error": "Database connection not available",
		})
	}

	// Query to get single article by ID
	query := `SELECT id, created_at, author_name, email_address, category_id,
			  article_title, article_body, message, accept_tc, paid, approved, url, keyword
			  FROM article WHERE id = ?`

	row := database.DBConn.QueryRow(query, articleID)

	var article Article
	var authorName, emailAddress, articleTitle, articleBody, message, url, keyword sql.NullString
	var categoryID sql.NullInt64
	var acceptTC, paid, approved sql.NullBool

	err := row.Scan(
		&article.ID,
		&article.CreatedAt,
		&authorName,
		&emailAddress,
		&categoryID,
		&articleTitle,
		&articleBody,
		&message,
		&acceptTC,
		&paid,
		&approved,
		&url,
		&keyword,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return c.Status(404).JSON(fiber.Map{
				"error": "Article not found",
			})
		}
		log.Printf("Error scanning article row: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch article",
		})
	}

	// Handle nullable fields
	if authorName.Valid {
		article.AuthorName = &authorName.String
	}
	if emailAddress.Valid {
		article.EmailAddress = &emailAddress.String
	}
	if categoryID.Valid {
		categoryIDInt := int(categoryID.Int64)
		article.CategoryID = &categoryIDInt
	}
	if articleTitle.Valid {
		article.ArticleTitle = &articleTitle.String
	}
	if articleBody.Valid {
		article.ArticleBody = &articleBody.String
	}
	if message.Valid {
		article.Message = &message.String
	}
	if acceptTC.Valid {
		article.AcceptTC = &acceptTC.Bool
	}
	if paid.Valid {
		article.Paid = &paid.Bool
	}
	if approved.Valid {
		article.Approved = &approved.Bool
	}
	if url.Valid {
		article.URL = &url.String
	}
	if keyword.Valid {
		article.Keyword = &keyword.String
	}

	log.Printf("Successfully retrieved article ID: %s", articleID)
	return c.JSON(fiber.Map{
		"success": true,
		"data":    article,
	})
}

func DeleteArticle(c *fiber.Ctx) error {
	// Get article ID from URL parameter
	articleID := c.Params("id")
	if articleID == "" {
		return c.Status(400).JSON(fiber.Map{
			"error": "Article ID is required",
		})
	}

	log.Printf("Deleting article with ID: %s", articleID)

	// Check if database connection is available
	if database.DBConn == nil {
		log.Printf("Database connection is nil")
		return c.Status(500).JSON(fiber.Map{
			"error": "Database connection not available",
		})
	}

	// Delete query to remove article by ID
	query := `DELETE FROM article WHERE id = ?`

	result, err := database.DBConn.Exec(query, articleID)
	if err != nil {
		log.Printf("Error deleting article: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to delete article",
		})
	}

	// Check if any rows were affected
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Printf("Error getting rows affected: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to verify article deletion",
		})
	}

	if rowsAffected == 0 {
		return c.Status(404).JSON(fiber.Map{
			"error": "Article not found",
		})
	}

	log.Printf("Successfully deleted article ID: %s", articleID)
	return c.JSON(fiber.Map{
		"success":    true,
		"message":    "Article deleted successfully",
		"article_id": articleID,
	})
}

func ApproveArticle(c *fiber.Ctx) error {
	// Get article ID from URL parameter
	articleID := c.Params("id")
	if articleID == "" {
		return c.Status(400).JSON(fiber.Map{
			"error": "Article ID is required",
		})
	}

	log.Printf("Approving article with ID: %s", articleID)

	// Check if database connection is available
	if database.DBConn == nil {
		log.Printf("Database connection is nil")
		return c.Status(500).JSON(fiber.Map{
			"error": "Database connection not available",
		})
	}

	// Update query to set approved = true for the specific article
	query := `UPDATE article SET approved = true WHERE id = ?`

	result, err := database.DBConn.Exec(query, articleID)
	if err != nil {
		log.Printf("Error updating article approval status: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to approve article",
		})
	}

	// Check if any rows were affected
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Printf("Error getting rows affected: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to verify article approval",
		})
	}

	if rowsAffected == 0 {
		return c.Status(404).JSON(fiber.Map{
			"error": "Article not found",
		})
	}

	log.Printf("Successfully approved article ID: %s", articleID)
	return c.JSON(fiber.Map{
		"success":    true,
		"message":    "Article approved successfully",
		"article_id": articleID,
	})
}

func GetContacts(c *fiber.Ctx) error {
	// Query to get all contacts from the database
	query := `SELECT id, created_at, full_name, company, subject, message, email
			  FROM contact ORDER BY created_at DESC`

	rows, err := database.DBConn.Query(query)
	if err != nil {
		log.Printf("Error querying contacts: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch contacts",
		})
	}
	defer rows.Close()

	var contacts []Contact

	for rows.Next() {
		var contact Contact
		var fullName, company, subject, message, email sql.NullString

		err := rows.Scan(
			&contact.ID,
			&contact.CreatedAt,
			&fullName,
			&company,
			&subject,
			&message,
			&email,
		)

		if err != nil {
			log.Printf("Error scanning contact row: %v", err)
			continue
		}

		// Handle nullable fields
		if fullName.Valid {
			contact.FullName = &fullName.String
		}
		if company.Valid {
			contact.Company = &company.String
		}
		if subject.Valid {
			contact.Subject = &subject.String
		}
		if message.Valid {
			contact.Message = &message.String
		}
		if email.Valid {
			contact.Email = &email.String
		}

		contacts = append(contacts, contact)
	}

	if err = rows.Err(); err != nil {
		log.Printf("Error iterating over contact rows: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to process contacts",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    contacts,
		"count":   len(contacts),
	})
}

func GetCategories(c *fiber.Ctx) error {
	log.Printf("GetCategories endpoint called")

	// Check if database connection is available
	if database.DBConn == nil {
		log.Printf("Database connection is nil")
		return c.Status(500).JSON(fiber.Map{
			"error": "Database connection not available",
		})
	}

	// Query to get all categories from the database
	query := `SELECT id, category_name, link FROM categories ORDER BY category_name ASC`

	log.Printf("Executing query: %s", query)
	rows, err := database.DBConn.Query(query)
	if err != nil {
		log.Printf("Error querying categories: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch categories",
		})
	}
	defer rows.Close()

	var categories []Category

	for rows.Next() {
		var category Category

		err := rows.Scan(
			&category.ID,
			&category.CategoryName,
			&category.Link,
		)

		if err != nil {
			log.Printf("Error scanning category row: %v", err)
			continue
		}

		categories = append(categories, category)
	}

	if err = rows.Err(); err != nil {
		log.Printf("Error iterating over category rows: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to process categories",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    categories,
		"count":   len(categories),
	})
}

func CreateCategory(c *fiber.Ctx) error {
	log.Printf("CreateCategory endpoint called")

	// Check if database connection is available
	if database.DBConn == nil {
		log.Printf("Database connection is nil")
		return c.Status(500).JSON(fiber.Map{
			"error": "Database connection not available",
		})
	}

	// Parse request body
	var category Category
	if err := c.BodyParser(&category); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate required fields
	if category.CategoryName == "" {
		return c.Status(400).JSON(fiber.Map{
			"error": "Category name is required",
		})
	}
	if category.Link == "" {
		return c.Status(400).JSON(fiber.Map{
			"error": "Link is required",
		})
	}

	log.Printf("Creating category: %s with link: %s", category.CategoryName, category.Link)

	// Insert query to create new category
	query := `INSERT INTO categories (category_name, link) VALUES (?, ?)`

	result, err := database.DBConn.Exec(query, category.CategoryName, category.Link)
	if err != nil {
		log.Printf("Error creating category: %v", err)
		// Check if it's a duplicate key error (unique constraint violation)
		if err.Error() == "Error 1062: Duplicate entry" ||
			err.Error() == "UNIQUE constraint failed" {
			return c.Status(409).JSON(fiber.Map{
				"error": "Category with this link already exists",
			})
		}
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create category",
		})
	}

	// Get the ID of the newly created category
	categoryID, err := result.LastInsertId()
	if err != nil {
		log.Printf("Error getting last insert ID: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to get category ID",
		})
	}

	// Set the ID in the category struct
	category.ID = int(categoryID)

	log.Printf("Successfully created category ID: %d", categoryID)
	return c.JSON(fiber.Map{
		"success": true,
		"message": "Category created successfully",
		"data":    category,
	})
}

func UnApproveArticle(c *fiber.Ctx) error {
	// Get article ID from URL parameter
	articleID := c.Params("id")
	if articleID == "" {
		return c.Status(400).JSON(fiber.Map{
			"error": "Article ID is required",
		})
	}

	log.Printf("Unapproving article with ID: %s", articleID)

	// Check if database connection is available
	if database.DBConn == nil {
		log.Printf("Database connection is nil")
		return c.Status(500).JSON(fiber.Map{
			"error": "Database connection not available",
		})
	}

	// Update query to set approved = false for the specific article
	query := `UPDATE article SET approved = false WHERE id = ?`

	result, err := database.DBConn.Exec(query, articleID)
	if err != nil {
		log.Printf("Error updating article approval status: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to unapprove article",
		})
	}

	// Check if any rows were affected
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Printf("Error getting rows affected: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to verify article unapproval",
		})
	}

	if rowsAffected == 0 {
		return c.Status(404).JSON(fiber.Map{
			"error": "Article not found",
		})
	}

	log.Printf("Successfully unapproved article ID: %s", articleID)
	return c.JSON(fiber.Map{
		"success":    true,
		"message":    "Article unapproved successfully",
		"article_id": articleID,
	})
}
