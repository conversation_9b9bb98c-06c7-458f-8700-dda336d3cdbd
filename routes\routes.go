package routes

import (
	"github.com/applegold/articles-backend/database"
	"github.com/gofiber/fiber/v2"
)

func Hello(c *fiber.Ctx) error {

	database.DBConn.Query("SELECT * FROM users")

	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func GetArticles(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func GetArticleSingle(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func DeleteArticle(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func GetContacts(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}
