package database

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

var (
	DBConn *sql.DB
)

const (
	host     = "localhost"
	port     = 3306 // Default port
	user     = "root"
	password = ""
	dbname   = "fiber_demo"
)

func Connect() error {
	var err error
	// Use DSN string to open
	DBConn, err = sql.Open("mysql", fmt.Sprintf("%s:%s@/%s", user, password, dbname))

	if err != nil {
		log.Fatal("Failed to connect to database. \n", err)
		return err
	}
	if err = DBConn.Ping(); err != nil {
		return err
	}
	return nil
}
