package database

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

var (
	DBConn *sql.DB
)

const (
	host     = "localhost"
	port     = 3306 // Default port
	user     = "root"
	password = ""
	dbname   = "techread"
)

func Connect() error {
	var err error
	// Use DSN string to open - more explicit format
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true", user, password, host, port, dbname)
	log.Printf("Attempting to connect to database with DSN: %s:***@tcp(%s:%d)/%s?parseTime=true", user, host, port, dbname)

	DBConn, err = sql.Open("mysql", dsn)
	if err != nil {
		log.Printf("Failed to open database connection: %v", err)
		return err
	}

	if err = DBConn.Ping(); err != nil {
		log.Printf("Failed to ping database: %v", err)
		log.Printf("Please check if MySQL/MariaDB is running on %s:%d", host, port)
		log.Printf("And verify database '%s' exists and user '%s' has access", dbname, user)
		return err
	}

	log.Printf("Successfully connected to database: %s", dbname)
	return nil
}
