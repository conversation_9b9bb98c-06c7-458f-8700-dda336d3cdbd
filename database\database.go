package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
)

var (
	DBConn *sql.DB
)

func Connect() error {
	// Load environment variables from .env file
	err := godotenv.Load()
	if err != nil {
		log.Printf("Warning: Error loading .env file: %v", err)
	}

	// Get database configuration from environment variables
	host := getEnv("LOCAL_HOST", "localhost")
	port := getEnv("INTERNAL_PORT", "3306")
	user := "root" // From your LOCAL_HOST_URL
	password := getEnv("MYSQL_ROOT_PASSWORD", "")
	dbname := "techread"

	// Use DSN string to open - more explicit format
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=true", user, password, host, port, dbname)
	log.Printf("Attempting to connect to database with DSN: %s:***@tcp(%s:%s)/%s?parseTime=true", user, host, port, dbname)

	DBConn, err = sql.Open("mysql", dsn)
	if err != nil {
		log.Printf("Failed to open database connection: %v", err)
		return err
	}

	if err = DBConn.Ping(); err != nil {
		log.Printf("Failed to ping database: %v", err)
		log.Printf("Please check if MySQL/MariaDB is running on %s:%s", host, port)
		log.Printf("And verify database '%s' exists and user '%s' has access", dbname, user)
		return err
	}

	log.Printf("Successfully connected to database: %s", dbname)
	return nil
}

// Helper function to get environment variable with fallback
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}
